package com.ydwf.bridge.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.ydwf.bridge.entity.ExportCsvModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("银代业绩明细清单VO -- 承保与预收与退保")
public class BankBussPrintVO extends ExportCsvModel implements Serializable {

    @ApiModelProperty(value = "分公司编码")
    @ExcelProperty(value = "分公司编码", index = 0)
    private String manageCom2Code;

    @ApiModelProperty(value = "分公司名称")
    @ExcelProperty(value = "分公司名称", index = 1)
    private String manageCom2Name;

    @ApiModelProperty(value = "中支编码")
    @ExcelProperty(value = "中支编码", index = 2)
    private String manageCom3Code;

    @ApiModelProperty(value = "中支名称")
    @ExcelProperty(value = "中支名称", index = 3)
    private String manageCom3Name;

    @ApiModelProperty(value = "营销服务部编码")
    @ExcelProperty(value = "营销服务部编码", index = 4)
    private String manageCom5Code;

    @ApiModelProperty(value = "营销服务部名称")
    @ExcelProperty(value = "营销服务部名称", index = 5)
    private String manageCom5Name;

    @ApiModelProperty(value = "总行编码")
    @ExcelProperty(value = "总行编码", index = 6)
    private String bankCode;

    @ApiModelProperty(value = "总行名称")
    @ExcelProperty(value = "银行名称", index = 7)
    private String bankName;

    @ApiModelProperty(value = "省分行编码")
    @ExcelProperty(value = "省分行编码", index = 8)
    private String provinceBankCode;

    @ApiModelProperty(value = "省分行名称")
    @ExcelProperty(value = "省分行名称", index = 9)
    private String provinceBankName;

    @ApiModelProperty(value = "市分行编码")
    @ExcelProperty(value = "市分行编码", index = 10)
    private String cityBankCode;

    @ApiModelProperty(value = "市分行名称")
    @ExcelProperty(value = "市分行名称", index = 11)
    private String cityBankName;

    @ApiModelProperty(value = "支行编码")
    @ExcelProperty(value = "支行编码", index = 12)
    private String branchBankCode;

    @ApiModelProperty(value = "支行名称")
    @ExcelProperty(value = "支行名称", index = 13)
    private String branchBankName;

    @ApiModelProperty(value = "网点编码")
    @ExcelProperty(value = "网点编码", index = 14)
    private String agentComCode;

    @ApiModelProperty(value = "网点名称")
    @ExcelProperty(value = "网点名称", index = 15)
    private String agentComName;

    @ApiModelProperty(value = "险种代码")
    @ExcelProperty(value = "险种代码", index = 16)
    private String riskCode;

    @ApiModelProperty(value = "险种名称")
    @ExcelProperty(value = "险种名称", index = 17)
    private String riskName;

    @ApiModelProperty(value = "投保单号")
    @ExcelProperty(value = "投保单号", index = 18)
    private String prtNo;

    @ApiModelProperty(value = "保单号")
    @ExcelProperty(value = "保单号", index = 19)
    private String contNo;

    @ApiModelProperty(value = "投保人")
    @ExcelProperty(value = "投保人", index = 20)
    private String appntName;

    @ApiModelProperty(value = "投保人客户号")
    @ExcelProperty(value = "投保人客户号", index = 21)
    private String appntNo;

    @ApiModelProperty(value = "被保险人")
    @ExcelProperty(value = "被保险人", index = 22)
    private String insuredName;

    @ApiModelProperty(value = "被保险人客户号")
    @ExcelProperty(value = "被保险人客户号", index = 23)
    private String insuredNo;

    @ApiModelProperty(value = "保险期限")
    @ExcelProperty(value = "保险期限", index = 24)
    private String insurancePeriod;

    @ApiModelProperty(value = "缴费年期")
    @ExcelProperty(value = "缴费期", index = 25)
    private String payPeriod;

    @ApiModelProperty(value = "缴费方式")
    @ExcelProperty(value = "缴费方式", index = 26)
    private String paymentWay;

    @ApiModelProperty(value = "保单年度")
    @ExcelProperty(value = "保单年度", index = 27)
    private String policyYear;

    @ApiModelProperty(value = "保费年度")
    @ExcelProperty(value = "保费年度", index = 28)
    private String premiumYear;

    @ApiModelProperty(value = "组合产品")
    @ExcelProperty(value = "组合产品", index = 29)
    private String productComName;

    @ApiModelProperty(value = "保险金额")
    @ExcelProperty(value = "保险金额", index = 30)
    @NumberFormat
    private BigDecimal amount;

    @ApiModelProperty(value = "规模保费")
    @ExcelProperty(value = "规模保费", index = 31)
    @NumberFormat
    private BigDecimal premium;

//    @ApiModelProperty(value = "提奖")
//    @ExcelProperty(value = "提奖", index = 32)
//    @NumberFormat
//    private BigDecimal prize;

    @ApiModelProperty(value = "基本法标准保费")
    @ExcelProperty(value = "基本法标准保费", index = 32)
    @NumberFormat
    private BigDecimal standardPremium;

    @ApiModelProperty(value = "财务标准保费")
    @ExcelProperty(value = "财务标准保费", index = 33)
    @NumberFormat
    private BigDecimal financeStandardPremium;

    @ApiModelProperty(value = "柜员代码")
    @ExcelProperty(value = "柜员代码", index = 34)
    private String sellerCode;

    @ApiModelProperty(value = "柜员姓名")
    @ExcelProperty(value = "柜员姓名", index = 35)
    private String sellerName;

    @ApiModelProperty(value = "柜员执业证编码")
    @ExcelProperty(value = "柜员执业证编码", index = 36)
    private String bankLicenseCode;

    @ApiModelProperty(value = "客户经理代码")
    @ExcelProperty(value = "客户经理代码", index = 37)
    private String agentCode;

    @ApiModelProperty(value = "客户经理姓名")
    @ExcelProperty(value = "客户经理姓名", index = 38)
    private String agentName;

    @ApiModelProperty(value = "执业证编码")
    @ExcelProperty(value = "执业证编码", index = 39)
    private String licenseNo;

    @ApiModelProperty(value = "项目经理职级")
    @ExcelProperty(value = "职级代码", index = 40)
    private String rankCode;

    @ApiModelProperty(value = "客户经理职级")
    @ExcelProperty(value = "客户经理职级", index = 41)
    private String rankName;

    @ApiModelProperty(value = "营业区编码")
    @ExcelProperty(value = "营业区编码", index = 42)
    private String areaCode;

    @ApiModelProperty(value = "营业区名称")
    @ExcelProperty(value = "营业区名称", index = 43)
    private String areaName;

    @ApiModelProperty(value = "营业区主管")
    @ExcelProperty(value = "营业区主管", index = 44)
    private String areaManagerCode;

    @ApiModelProperty(value = "营业区主管姓名")
    @ExcelProperty(value = "营业区主管姓名", index = 45)
    private String areaManagerName;

    @ApiModelProperty(value = "营业部编码")
    @ExcelProperty(value = "营业部编码", index = 46)
    private String deptCode;

    @ApiModelProperty(value = "营业部名称")
    @ExcelProperty(value = "营业部名称", index = 47)
    private String deptName;

    @ApiModelProperty(value = "营业部主管")
    @ExcelProperty(value = "营业部主管", index = 48)
    private String deptManagerCode;

    @ApiModelProperty(value = "营业部主管姓名")
    @ExcelProperty(value = "营业部主管姓名", index = 49)
    private String deptManagerName;

    @ApiModelProperty(value = "销售渠道主渠道")
    @ExcelProperty(value = "销售渠道主渠道", index = 50)
    private String saleChannel;

    @ApiModelProperty(value = "销售子渠道")
    @ExcelProperty(value = "销售子渠道", index = 51)
    private String sellType;

    @ApiModelProperty(value = "三级渠道")
    @ExcelProperty(value = "三级渠道(投保来源)", index = 52)
    private String agentType;

    @ApiModelProperty(value = "交易类型")
    @ExcelProperty(value = "交易类型", index = 53)
    private String tradeType;

    @ApiModelProperty(value = "保单状态")
    @ExcelProperty(value = "保单状态", index = 54)
    private String contState;

    @ApiModelProperty(value = "入机日期")
    @ExcelProperty(value = "入机日期", index = 55)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date makeDate;

    @ApiModelProperty(value = "保单投保日期")
    @ExcelProperty(value = "投保日期", index = 56)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date polApplyDate;

    @ApiModelProperty(value = "保单投保时间")
    @ExcelProperty(value = "投保时间", index = 57)
    private String polApplyTime;

    @ApiModelProperty(value = "保单承保日期")
    @ExcelProperty(value = "承保日期", index = 58)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date signDate;

    @ApiModelProperty(value = "保单承保时间")
    @ExcelProperty(value = "承保时间", index = 59)
    private String signTime;

    @ApiModelProperty(value = "保单生效日期")
    @ExcelProperty(value = "生效日期", index = 60)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date cvaliDate;

    @ApiModelProperty(value = "客户签收日期")
    @ExcelProperty(value = "客户签收日期", index = 61)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date customerSignDate;

    @ApiModelProperty(value = "保单收到日期")
    @ExcelProperty(value = "保单收到日期", index = 62)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date policyReceiveDate;

    @ApiModelProperty(value = "回单入机日期")
    @ExcelProperty(value = "回单入机日期", index = 63)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date receiptMakeDate;

    @ApiModelProperty(value = "受理日期")
    @ExcelProperty(value = "受理日期", index = 64)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date inputDate;

    @ApiModelProperty(value = "退保日期")
    @ExcelProperty(value = "退保日期", index = 65)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date xtDate;

    @ApiModelProperty(value = "回访成功日期")
    @ExcelProperty(value = "回访成功日期", index = 66)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date visitDate;

    @ApiModelProperty(value = "犹豫期截止日期")
    @ExcelProperty(value = "犹豫期截止日期", index = 67)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date hesitateEnd;

    @ApiModelProperty(value = "是否犹豫期")
    @ExcelProperty(value = "是否过犹豫期", index = 68)
    private String isHesitate;

    @ApiModelProperty(value = "保险止期")
    @ExcelProperty(value = "保险止期", index = 69)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date insEndDate;

    @ApiModelProperty(value = "退保金额")
    @ExcelProperty(value = "退保金额", index = 70)
    @NumberFormat
    private BigDecimal ctMoney;

    @ApiModelProperty(value = "犹豫期天数")
    @ExcelProperty(value = "犹豫期天数", index = 71)
    private String hesitateDay;
}
