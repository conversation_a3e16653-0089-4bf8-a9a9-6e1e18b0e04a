<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="REPORT">
    <select id="bankbussprintbyprepaydate"
            parameterType="com.ydwf.bridge.requestbean.BankBussWrap"
            resultType="com.ydwf.bridge.vo.BankBussPrintVO">
        select * from (
        select t.* , ROWNUM RN from (
        select x.* from (

        select substr(c.managecom, 0, 4) manageCom2Code,
        (select name from ldcom where comcode = substr(c.managecom, 0, 4)) manageCom2Name, substr(c.managecom, 0, 6) manageCom3Code,
        (select name from ldcom where comcode = substr(c.managecom, 0, 6)) manageCom3Name,
        c.managecom manageCom5Code, (select name from ldcom where comcode = c.managecom) manageCom5Name,

        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select agentcom from lacom where banktype = '00' start with agentcom = c.agentcom connect by  agentcom = prior upagentcom) else '' end bankCode,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select name from lacom where banktype = '00' start with agentcom = c.agentcom connect by  agentcom = prior upagentcom) else '' end bankName,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select agentcom from lacom where banktype = '01' start with agentcom = c.agentcom connect by  agentcom = prior upagentcom) else '' end provinceBankCode,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select name from lacom where banktype = '01' start with agentcom = c.agentcom connect by  agentcom = prior upagentcom) else '' end provinceBankName,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select agentcom from lacom where banktype = '02' start with agentcom = c.agentcom connect by  agentcom = prior upagentcom) else '' end cityBankCode ,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select name from lacom where banktype = '02' start with agentcom = c.agentcom connect by  agentcom = prior upagentcom) else '' end cityBankName,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select agentcom from lacom where banktype = '03' start with agentcom = c.agentcom connect by  agentcom = prior upagentcom) else '' end branchBankCode,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select name from lacom where banktype = '03' start with agentcom = c.agentcom connect by  agentcom = prior upagentcom) else '' end branchBankName,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then c.agentcom else '' end agentComCode,
        case when c.salechnl || c.selltype in ('0308', '0302', '0402') then (select name from lacom where agentcom = c.agentcom) else '' end agentComName,

        p.riskcode riskCode, (select riskname from lmriskapp where riskcode = p.riskcode) riskName,
        c.prtno prtNo, c.contno contNo, c.appntname appntName,c.appntno appntNo, c.insuredname insuredName,c.insuredno insuredNo,
        case when p.insuyearflag = 'Y' and p.insuyear >= 500 then '终身' when p.insuyearflag = 'Y' then p.insuyear||'年' when p.insuyearflag = 'M' then p.insuyear||'月' when p.insuyearflag = 'D' then p.insuyear||'天' when p.insuyearflag = 'A' then p.insuyear||'岁' end insurancePeriod,
        case when p.payintv = '0' then '趸缴' else to_char(p.payyears) || '年' end payPeriod,
        (select codename from ldcode where codetype ='payintv' and code=p.payintv) paymentWay,
        trunc(months_between(sysdate, p.cvalidate) / 12, 0) + 1 policyYear,
        (SELECT nvl(max(decode(mainpolyear,0,1,mainpolyear)),1) FROM ljapayperson k WHERE contno = c.contno and
        not exists (SELECT 1  FROM ljapayperson  WHERE contno = k.contno and paycount = k.paycount  and nvl(getnoticeno,1) = nvl(k.getnoticeno,1)  and 0 > sumactupaymoney )) premiumYear,
        nvl((select productcomname from LMProductCommain where productcomcode =c.productcomcode),'无') productComName,
        p.amnt amount,
        p.prem premium,
--         round(p.prem *NVL(GETBKFYCRATE(A.Branchtype,p.riskcode,p.payintv,p.years,p.payyears,nvl((select decode(bankcode, '80', '80', '00') from lacom where lacom.agentcom = p.agentcom),'80'),p.managecom,0,'01', nvl(c.productcomcode,'1'), c.signdate),0),2) prize,
        round(p.prem * NVL(GETSTANDRATE(A.Branchtype,p.riskcode,p.payintv,p.years,p.payyears,nvl((select decode(bankcode, '80', '80', '00') from lacom where lacom.agentcom =p.agentcom),'80'),'01', nvl(c.productcomcode,'1'),c.signdate),0),2) standardPremium,
        (SELECT DISTINCT BANKAGENTCODE FROM lcbankservinfo where prtno = c.prtno) sellerCode,
        (SELECT DISTINCT BANKAGENTNAME FROM lcbankservinfo where prtno = c.prtno) sellerName,
        h.agentcode agentCode, k.name agentName,
        (select qualifno  from laqualification where agentcode = a.agentcode and idx = '02')  licenseNo,
        nvl((case when  c.signdate is null then x.agentgrade else h.agentgrade end),x.agentgrade) rankCode,(case when  c.signdate is null then (select codename from ldcode where codetype = 'agentgrade3' and code = x.agentgrade) else (select codename from ldcode where codetype = 'agentgrade3' and code =nvl(h.agentgrade,x.agentgrade) ) end) rankName,
        h.upbranchattr  areaCode,h.upbranchname areaName,
        (select h.upbranchmanager from dual where a.agentcode != h.upbranchmanager)  areaManagerCode, (select h.upbranchmanagername from dual where a.agentcode != h.upbranchmanager)  areaManagerName, h.branchattr deptCode,
        h.branchname deptName, (select h.branchmanager from dual where h.branchmanager != a.agentcode)   deptManagerCode,(select h.branchmanagername from dual where h.branchmanager != a.agentcode)  deptManagerName,
        (SELECT l.codename FROM ldcode l WHERE l.code = c.salechnl and l.codetype = 'salechnl') as saleChannel, decode(c.salechnl,'01',(SELECT n.codename  FROM ldcode1 n WHERE n.code = c.salechnl and n.code1 = c.agenttype and n.codetype = 'selltype'), (SELECT n.codename FROM ldcode1 n  WHERE n.code = c.salechnl  and n.code1 = c.selltype and n.codetype = 'selltype')) as sellType,decode(c.salechnl || c.selltype || c.agenttype,'0308','柜面','010101',(SELECT codename FROM ldcode  WHERE code = c.agenttype and codealias = c.salechnl and comcode = c.selltype and codetype = 'BankSubChannel'),decode(c.agenttype,'', '',nvl((SELECT codename FROM ldcode WHERE code = c.agenttype and codetype = 'BankSubChannel' AND rownum = 1),'经代通微信'))) agentType,'承保收费' tradeType,
        (case when p.appflag = '1' then '承保' when p.appflag = '4' then
        (case when exists (select 'X' from lpedoritem where edortype = 'WT' and edorstate = '0' and contno = c.contno
        and exists (select 'Y' from lppol where edorno = lpedoritem.edorno and polno = p.polno)) then '犹豫期退保'
        when exists (select 'X' from lpedoritem where edortype = 'CT' and edorstate = '0' and contno = c.contno
        and exists (select 'Y' from lppol where edorno = lpedoritem.edorno and polno = p.polno)) then '退保'
        when exists (select 'X' from lpedoritem where edortype = 'XT' and edorstate = '0' and contno = c.contno
        and exists (select 'Y' from lppol where edorno = lpedoritem.edorno and polno = p.polno)) then '协议退保'
        else '终止' end)
        when p.appflag = '0' then '投保'
        when p.appflag = '2' then '附加险投保'
        when p.appflag = 'B' then '银保通投保'
        when p.appflag = 'F' then '银保通对账失败'
        when p.appflag = '9' then '自动续保期间'
        else p.appflag || p.uwflag end) contState,




        p.makedate makeDate, p.polapplydate polApplyDate,p.maketime polApplyTime, p.signdate signDate,p.signtime signTime, p.cvalidate cvaliDate,
        c.customgetpoldate customerSignDate, c.customgetpoldate policyReceiveDate, c.getpoldate receiptMakeDate, c.inputdate inputDate,
        (case when exists (select 'X' from lpedoritem where edortype in ('WT','CT','XT') and edorstate = '0' and contno = c.contno
        and exists (select 'Y' from lppol where edorno = lpedoritem.edorno and polno = p.polno ))
        then nvl((select max(EDORVALIDATE) from lpedoritem where edortype in ('WT','CT','XT') and edorstate  = '0' and  contno = c.contno
        and edorno in (SELECT endorsementno FROM LJAGETENDORSE where feeoperationtype in ('WT','CT','XT') and contno=c.contno   and polno =p.polno)),
        (select max(EDORVALIDATE) from lpedoritem where edortype in ('WT','CT','XT') and edorstate  = '0' and  contno = c.contno)) else null end) xtDate ,

        (SELECT min(visitdate) from lcvisitresult WHERE contno = c.contno AND visitresult like '%成功%') visitDate,
--         CASE WHEN ((C.SALECHNL || C.SELLTYPE IN ('0308')) AND SUBSTR(C.AGENTCOM, 1, 2) IN ('01', '02') AND P.SPECIFYVALIDATE = 'Y' AND C.CVALIDATE = DATE '2020-01-01')
--         THEN CASE WHEN  FN_GET_WT_DATE(C.CONTNO)>( C.CVALIDATE + 15 ) AND FN_GET_WT_DATE(C.CONTNO) IS NOT NULL THEN FN_GET_WT_DATE(C.CONTNO)  ELSE ( C.CVALIDATE + 15 ) END
--         when substr(c.managecom, 1, 6) = '864403' then
--         case when FN_GET_WT_STR(c.contno,'days') is null then  null else FN_GET_WT_DATE(c.contno) end
--         else c.customgetpoldate + FN_GET_WT_STR(c.contno,'days')
--         end hesitateEnd,
        c.hesitateenddate hesitateEnd,

        <!--case when substr(c.managecom, 1, 6) = '864403' then
        case when FN_GET_WT_DATE(c.contno) >= to_date(to_char(sysdate,'yyyymmdd') ,'yyyymmdd') then case when FN_GET_WT_STR(c.contno,'days') is null or c.customgetpoldate is null then '' else  '否' end else  case when FN_GET_WT_DATE(c.contno) is null or FN_GET_WT_STR(c.contno,'days') is null then '' else '是' end end else
        case when FLOOR(TO_NUMBER(sysdate - c.customgetpoldate)) >FN_GET_WT_STR(c.contno,'days') then
        '是' else case when FN_GET_WT_STR(c.contno,'days') is null or c.customgetpoldate is null then '' else '否' end end end isHesitate , -->
        CASE
            WHEN c.hesitateenddate IS NULL THEN NULL
            WHEN c.hesitateenddate >= TRUNC(SYSDATE) THEN '否'
            ELSE '是'
        END isHesitate,
        case when p.insuyear = 1000 then  p.enddate-1 else p.enddate end insEndDate,
        ((select nvl(sum(getmoney), 0)  from ljagetendorse  where polno = p.polno and ((feeoperationtype in ('CT', 'XT') and   SUBFEEOPERATIONTYPE = 'G006') or   (SUBFEEOPERATIONTYPE = 'G001' and feeoperationtype = 'WT')))
        - (select nvl(sum(getmoney), 0)  from ljagetendorse where polno = p.polno and  SUBFEEOPERATIONTYPE IN ('G006','G001')  and  feeoperationtype = 'RB')) ctMoney,
        (SELECT DISTINCT banklicensecode FROM lcbankservinfo where prtno = c.prtno) bankLicenseCode,
        c.HESITATEDAY hesitateDay,
        -- 长短险标识
        (select decode(riskperiod,'L','长险','M','短险','S','极短险') from lmriskapp where riskcode = p.riskcode)
        riskCycle,
        -- 折标系数
        CASE
            WHEN riskCycle = '短险' THEN 1
            WHEN riskCycle = '长险' THEN
            CASE
                WHEN p.payintv = '一次性交清' THEN 0.1
                WHEN p.payintv = '年交' AND p.premterm <= 10 THEN 0.1 * p.premterm
                WHEN p.payintv = '年交' AND p.premterm > 10 THEN 1
                ELSE NULL
            END
            ELSE NULL
        END AS scalingFactor,
        CASE
            WHEN scalingFactor IS NULL THEN NULL
            ELSE scalingFactor IS NOT NULL THEN round(p.prem * scalingFactor, 2)
            ELSE NULL
        END AS financeStandardPremium

        from lcpol p, laagent a, labranchgroup b, lccont c ,latree x,laagenttocont h,laagent k
        where c.agentgroup = b.agentgroup and c.contno =h.contno and x.agentcode = a.agentcode and h.agentcode=k.agentcode
        and c.agentcode = a.agentcode and c.contno = p.contno  and a.branchtype ='3' and a.managecom not in ('**********','**********','**********','**********','**********') and p.uwflag not in ('a','1','2')

        and not exists(select 1 from lacom where agentcom=c.agentcom and bankcode like '04%' and c.salechnl='03' and c.selltype='08')


        <if test="manageCom != null and manageCom !=''">
            and (c.managecom like concat(#{manageCom},'%')
            <choose>
                <when test="manageCom.startsWith('864404'.toString())">
                    and c.managecom != '**********'
                </when>
                <when test="manageCom.startsWith('********'.toString())">
                    or c.managecom = '**********'
                </when>
            </choose>
            )
        </if>
<!--        <if test="agentCom != null and agentCom !=''">-->
<!--            and c.agentcom like concat(#{agentCom},'%')-->
<!--        </if>-->
        <if test="agentComCodeList!=null and agentComCodeList.size()>0 ">
            and c.agentcom in
            <foreach collection="agentComCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="riskCode != null and riskCode !=''">
            and p.riskcode = #{riskCode}
        </if>
        <if test="agentCode != null and agentCode !=''">
            and c.agentcode = #{agentCode}
        </if>
        <if test="startDate != null and startDate != ''">
            and c.inputdate >= to_date( #{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate != ''">
            and to_date( #{endDate},'YYYY-MM-DD HH24:MI:SS') >= c.inputdate
        </if>
        <if test="comCodeFlag == true">
            and c.managecom !='8644040501'
        </if>

        ) x

        <if test="orderType != null and orderType != ''">
            <choose>
                <when test="orderType.equals('01'.toString())">
                    order by x.manageCom5Code asc, x.signDate, x.contNo, x.tradeType
                </when>
                <when test="orderType.equals('02'.toString())">
                    order by x.agentComCode asc, x.signDate, x.contNo, x.tradeType
                </when>
            </choose>
        </if>
        ) t

        <if test = "COUNT != null">
            where ROWNUM <![CDATA[<=]]> #{COUNT}
        </if>
        )
        <if test="BEGIN != null">
            where RN >#{BEGIN}
        </if>

    </select>

</mapper>
